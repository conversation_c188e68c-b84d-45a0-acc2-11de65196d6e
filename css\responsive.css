/* ===== Responsive Design ===== */

/* Extra Large Devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
}

/* Large Devices (1200px and up) */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-content {
        gap: var(--spacing-2xl);
    }
}

/* Medium Devices (992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .nav-list {
        gap: var(--spacing-lg);
    }
}

/* Small Devices (768px and up) */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 var(--spacing-md);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    /* Header Layout for Tablets */
    .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .nav-brand {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .nav-actions .btn {
        display: none !important;
    }

    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    /* Mobile Menu Active State */
    .nav-menu.active {
        display: flex;
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid var(--gray-200);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 998;
        padding: var(--spacing-lg) 0;
    }

    .nav-menu.active .nav-list {
        flex-direction: column;
        width: 100%;
        gap: 0;
        padding: 0 var(--spacing-lg);
    }

    .nav-menu.active .nav-link {
        display: block;
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 1px solid var(--gray-100);
        font-size: var(--font-size-lg);
        font-weight: 600;
    }

    .nav-menu.active .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: var(--gray-50);
        margin-top: 0;
        border-radius: 0;
    }

    .nav-menu.active .dropdown-menu a {
        padding: var(--spacing-sm) var(--spacing-xl);
        font-size: var(--font-size-base);
        border-bottom: 1px solid var(--gray-200);
    }

    /* Mobile Menu Toggle Animation */
    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    /* Prevent body scroll when menu is open */
    body.menu-open {
        overflow: hidden;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .tab-buttons {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        flex: 1;
        min-width: 150px;
    }
}

/* Extra Small Devices (576px and up) */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        min-height: 80vh;
        padding: var(--spacing-3xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .nav-brand .brand-name {
        font-size: var(--font-size-xl);
    }
    
    .features {
        padding: var(--spacing-2xl) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-xl);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .services {
        padding: var(--spacing-2xl) 0;
    }
    
    .tab-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .tab-btn {
        width: 100%;
        text-align: center;
    }
    
    .tab-content {
        padding: var(--spacing-xl);
    }
    
    .hero-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
        max-width: 100%;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 120px;
    }

    .floating-card .icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
}

/* Mobile Devices (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    /* Header Adjustments */
    .navbar {
        padding: var(--spacing-md) 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }

    .navbar .container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 var(--spacing-lg);
    }

    .nav-brand {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        background: none;
        padding: 0;
        border-radius: 0;
        box-shadow: none;
        border: none;
    }

    .logo {
        height: 40px;
    }

    .brand-name {
        font-size: var(--font-size-xl);
        font-weight: 800;
    }

    .nav-actions {
        grid-column: 3;
        gap: var(--spacing-sm);
    }

    .nav-actions .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }

    /* Mobile Menu for Small Screens */
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .nav-menu.active {
        display: flex;
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        z-index: 998;
        padding: 0;
    }

    .nav-menu.active .nav-list {
        width: 100%;
        flex-direction: column;
        gap: 0;
        padding: 0;
        margin: 0;
    }

    .nav-menu.active .nav-list li {
        width: 100%;
        border-bottom: 1px solid #f1f5f9;
    }

    .nav-menu.active .nav-list li:last-child {
        border-bottom: none;
    }

    .nav-menu.active .nav-list .nav-link {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 18px 25px;
        font-size: 16px;
        font-weight: 500;
        color: #374151;
        text-decoration: none;
        transition: all 0.3s ease;
        width: 100%;
        text-align: center;
    }

    .nav-menu.active .nav-list .nav-link i {
        font-size: 18px;
        color: #6b7280;
        transition: all 0.3s ease;
    }

    .nav-menu.active .nav-list .nav-link:hover,
    .nav-menu.active .nav-list .nav-link.active {
        background: #f8fafc;
        color: #2563eb;
    }

    .nav-menu.active .nav-list .nav-link:hover i,
    .nav-menu.active .nav-list .nav-link.active i {
        color: #2563eb;
    }

    .nav-menu.active .nav-list .nav-auth-section .nav-link {
        margin: 8px 15px;
        border-radius: 10px;
        font-weight: 600;
    }

    .nav-menu.active .nav-list .nav-link-login {
        background: #f1f5f9;
        color: #374151;
        border: 2px solid #e2e8f0;
    }

    .nav-menu.active .nav-list .nav-link-login:hover {
        background: #e2e8f0;
        border-color: #cbd5e1;
    }

    .nav-menu.active .nav-list .nav-link-register {
        background: #10b981;
        color: #ffffff;
        border: 2px solid #10b981;
    }

    .nav-menu.active .nav-list .nav-link-register:hover {
        background: #059669;
        border-color: #059669;
    }
    
    /* Language Switcher */
    .language-switcher-fixed {
        top: 50%;
        left: 0;
        transform: translateY(-50%);
    }

    .lang-btn {
        width: 50px;
        height: 50px;
        font-size: 1.1rem;
        border-radius: 0 20px 20px 0;
    }

    /* Contact Buttons */
    .contact-buttons-fixed {
        bottom: var(--spacing-lg);
        right: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .contact-btn {
        width: 60px;
        height: 60px;
        font-size: 1.3rem;
    }
    
    /* Hero Section */
    .hero {
        min-height: 70vh;
        padding: var(--spacing-2xl) 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-lg);
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .btn-lg {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-base);
    }
    
    .hero-image {
        order: -1;
    }
    
    .hero-graphic {
        height: 300px;
    }
    
    .hero-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        max-width: 280px;
        margin: 0 auto;
    }

    .floating-card {
        padding: var(--spacing-lg);
        font-size: var(--font-size-sm);
        min-height: 140px;
        gap: var(--spacing-sm);
    }

    .floating-card .icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-md);
    }

    .floating-card h4 {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-sm);
    }

    .floating-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Sections */
    .features,
    .services {
        padding: var(--spacing-xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .section-subtitle {
        font-size: var(--font-size-sm);
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .feature-card h3 {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-sm);
    }
    
    .feature-card p {
        font-size: var(--font-size-sm);
    }
    
    /* Services */
    .tab-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .tab-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .tab-content {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }
    
    /* Scroll Indicator */
    .scroll-indicator {
        bottom: var(--spacing-md);
    }
    
    .scroll-arrow {
        height: 20px;
    }
}

/* Landscape Mobile Devices */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        min-height: 100vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
        text-align: left;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-md);
    }
    
    .btn-lg {
        width: auto;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .header,
    .language-switcher,
    .hero-buttons,
    .scroll-indicator {
        display: none !important;
    }
    
    .hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-text {
        color: var(--gray-900) !important;
    }
    
    .section-title,
    .hero-title {
        color: var(--gray-900) !important;
    }
    
    .feature-card,
    .tab-content {
        box-shadow: none !important;
        border: 1px solid var(--gray-300) !important;
    }
}

/* Contact Section Responsive Improvements */
@media (max-width: 768px) {
    .contact-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .contact-info .pricing-card {
        width: 100%;
        max-width: 350px;
        text-align: center;
        margin: 0 auto;
    }

    .contact-form-wrapper {
        display: flex;
        justify-content: center;
        padding: 0 var(--spacing-md);
    }

    .contact-form {
        width: 100%;
        max-width: 500px;
        margin: 0 auto;
    }

    .contact-form .form-row {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .contact-form .form-group {
        width: 100%;
    }
}

@media (max-width: 575px) {
    .contact-info .pricing-card {
        max-width: 300px;
        padding: var(--spacing-lg);
    }

    .contact-form-wrapper {
        padding: 0 var(--spacing-sm);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }

    .contact-form .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Accessibility - Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .floating-card,
    .hero-particles,
    .scroll-arrow {
        animation: none !important;
    }

    .animate-fade-up,
    .animate-fade-left,
    .animate-fade-right,
    .animate-on-scroll {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1f2937;
        --gray-50: #111827;
        --gray-100: #1f2937;
        --gray-200: #374151;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    .header {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--gray-700);
    }
    
    .dropdown-menu {
        background: var(--gray-800);
        border: 1px solid var(--gray-700);
    }
    
    .lang-btn {
        background: var(--gray-800);
        border-color: var(--gray-700);
        color: var(--gray-200);
    }
}
